import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/note_model.dart';
import '../services/database_helper.dart';
import '../services/notification_service.dart';
import 'add_note_screen.dart';
import 'settings_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with TickerProviderStateMixin {
  final DatabaseHelper _dbHelper = DatabaseHelper();
  final NotificationService _notificationService = NotificationService();
  List<NoteModel> _notes = [];
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadNotes();
    _initializeNotifications();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _initializeNotifications() async {
    await _notificationService.initialize();
    await _notificationService.schedulePeriodicCheck();
  }

  Future<void> _loadNotes() async {
    final notes = await _dbHelper.getAllNotes();
    setState(() {
      _notes = notes;
    });
  }

  List<NoteModel> _getNotesForTab(int index) {
    switch (index) {
      case 0: // Tutte
        return _notes;
      case 1: // Oggi
        return _notes
            .where((note) => note.isDueToday() && !note.isCompleted)
            .toList();
      case 2: // Scadute
        return _notes.where((note) => note.isOverdue()).toList();
      case 3: // Completate
        return _notes.where((note) => note.isCompleted).toList();
      default:
        return _notes;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Scadenziario'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () async {
              await _notificationService.checkAndSendNotifications();
              _loadNotes();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Controllo scadenze completato')),
              );
            },
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const SettingsScreen()),
              );
            },
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Tutte'),
            Tab(text: 'Oggi'),
            Tab(text: 'Scadute'),
            Tab(text: 'Completate'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildNotesList(0),
          _buildNotesList(1),
          _buildNotesList(2),
          _buildNotesList(3),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () async {
          final result = await Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const AddNoteScreen()),
          );
          if (result == true) {
            _loadNotes();
          }
        },
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildNotesList(int tabIndex) {
    final notes = _getNotesForTab(tabIndex);

    if (notes.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.note_add, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'Nessuna nota trovata',
              style: TextStyle(fontSize: 18, color: Colors.grey[600]),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadNotes,
      child: ListView.builder(
        padding: const EdgeInsets.all(8),
        itemCount: notes.length,
        itemBuilder: (context, index) {
          final note = notes[index];
          return _buildNoteCard(note);
        },
      ),
    );
  }

  Widget _buildNoteCard(NoteModel note) {
    Color cardColor = Colors.white;
    Color borderColor = Colors.grey[300]!;
    IconData statusIcon = Icons.schedule;
    Color statusColor = Colors.blue;

    if (note.isCompleted) {
      cardColor = Colors.green[50]!;
      borderColor = Colors.green[300]!;
      statusIcon = Icons.check_circle;
      statusColor = Colors.green;
    } else if (note.isOverdue()) {
      cardColor = Colors.red[50]!;
      borderColor = Colors.red[300]!;
      statusIcon = Icons.warning;
      statusColor = Colors.red;
    } else if (note.isDueToday()) {
      cardColor = Colors.orange[50]!;
      borderColor = Colors.orange[300]!;
      statusIcon = Icons.today;
      statusColor = Colors.orange;
    }

    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
      color: cardColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: borderColor, width: 1),
      ),
      child: ListTile(
        leading: Icon(statusIcon, color: statusColor),
        title: Text(
          note.title,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            decoration: note.isCompleted ? TextDecoration.lineThrough : null,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(note.description),
            const SizedBox(height: 4),
            Text(
              'Scadenza: ${DateFormat('dd/MM/yyyy').format(note.dueDate)}',
              style: TextStyle(color: statusColor, fontWeight: FontWeight.w500),
            ),
            if (note.emailRecipient != null)
              Text(
                'Email: ${note.emailRecipient}',
                style: TextStyle(color: Colors.grey[600], fontSize: 12),
              ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) => _handleMenuAction(value, note),
          itemBuilder: (context) => [
            if (!note.isCompleted)
              const PopupMenuItem(
                value: 'complete',
                child: Row(
                  children: [
                    Icon(Icons.check, color: Colors.green),
                    SizedBox(width: 8),
                    Text('Completa'),
                  ],
                ),
              ),
            const PopupMenuItem(
              value: 'edit',
              child: Row(
                children: [
                  Icon(Icons.edit, color: Colors.blue),
                  SizedBox(width: 8),
                  Text('Modifica'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete, color: Colors.red),
                  SizedBox(width: 8),
                  Text('Elimina'),
                ],
              ),
            ),
          ],
        ),
        isThreeLine: true,
      ),
    );
  }

  void _handleMenuAction(String action, NoteModel note) async {
    switch (action) {
      case 'complete':
        await _dbHelper.markAsCompleted(note.id!);
        _loadNotes();
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('Nota completata!')));
        break;
      case 'edit':
        final result = await Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => AddNoteScreen(noteToEdit: note),
          ),
        );
        if (result == true) {
          _loadNotes();
        }
        break;
      case 'delete':
        final confirm = await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Conferma eliminazione'),
            content: const Text('Sei sicuro di voler eliminare questa nota?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: const Text('Annulla'),
              ),
              TextButton(
                onPressed: () => Navigator.pop(context, true),
                child: const Text('Elimina'),
              ),
            ],
          ),
        );
        if (confirm == true) {
          await _dbHelper.deleteNote(note.id!);
          _loadNotes();
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(const SnackBar(content: Text('Nota eliminata!')));
        }
        break;
    }
  }
}

# Scadenziario - Deadline Scheduler

Un'applicazione Flutter cross-platform per la gestione delle scadenze con notifiche email automatiche.

## Caratteristiche

### 📱 **Versione Mobile (Android/iOS)**
- Interfaccia ottimizzata per dispositivi mobili
- Notifiche push in background
- Gestione completa delle note con scadenze
- Invio automatico di email di promemoria

### 🖥️ **Versione Desktop (Windows/macOS/Linux)**
- Interfaccia a tre pannelli ottimizzata per schermi grandi
- Sidebar con filtri e contatori
- Pannello centrale con lista delle note
- Pannello dettagli per visualizzazione completa
- Controllo periodico delle scadenze ogni ora

## Funzionalità Principali

### ✅ **Gestione Note**
- Creazione, modifica ed eliminazione di note
- Titolo, descrizione e data di scadenza
- Stato di completamento
- Destinatario email personalizzabile per ogni nota

### 📧 **Notifiche Email**
- Configurazione server SMTP personalizzabile
- Invio automatico di email quando una nota scade
- Supporto per email multiple raggruppate
- Template email personalizzabili

### 🔍 **Filtri e Ricerca**
- Visualizza tutte le note
- Filtra per note in scadenza oggi
- Filtra per note scadute
- Filtra per note completate
- Contatori automatici per ogni categoria

### 🎨 **Interfaccia Utente**
- Design Material 3
- Supporto tema chiaro/scuro automatico
- Localizzazione italiana
- Interfaccia responsive per mobile e desktop

## Installazione e Setup

### Prerequisiti
- Flutter SDK (versione 3.8.0 o superiore)
- Dart SDK
- Per Windows: Visual Studio 2022 con C++ build tools

### Dipendenze Principali
```yaml
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  sqflite: ^2.3.0              # Database mobile
  sqflite_common_ffi: ^2.3.0   # Database desktop
  mailer: ^6.0.1               # Invio email
  workmanager: ^0.5.2          # Background tasks (mobile)
  shared_preferences: ^2.2.2   # Configurazioni
  intl: ^0.20.2                # Localizzazione
```

### Installazione
1. Clona il repository
2. Esegui `flutter pub get` per installare le dipendenze
3. Per mobile: `flutter run`
4. Per Windows: `flutter run -d windows`
5. Per macOS: `flutter run -d macos`
6. Per Linux: `flutter run -d linux`

## Configurazione Email

### Impostazioni SMTP
L'applicazione richiede la configurazione di un server SMTP per l'invio delle email:

1. Apri le impostazioni dall'interfaccia
2. Configura i seguenti parametri:
   - **Server SMTP**: es. smtp.gmail.com
   - **Porta**: es. 587 (TLS) o 465 (SSL)
   - **Username**: il tuo indirizzo email
   - **Password**: password dell'account o app password
   - **Email mittente**: indirizzo email mittente
   - **Nome mittente**: nome visualizzato nelle email

### Esempio Configurazione Gmail
- Server: smtp.gmail.com
- Porta: 587
- Sicurezza: TLS
- Username: <EMAIL>
- Password: [App Password generata da Google]

## Architettura

### Struttura del Progetto
```
lib/
├── main.dart                 # Entry point con rilevamento piattaforma
├── models/
│   └── note_model.dart      # Modello dati delle note
├── screens/
│   ├── home_screen.dart     # Interfaccia mobile
│   ├── desktop_home_screen.dart  # Interfaccia desktop
│   └── add_note_screen.dart # Schermata aggiunta/modifica note
├── services/
│   ├── database_helper.dart # Gestione database SQLite
│   ├── email_service.dart   # Servizio invio email
│   ├── notification_service.dart        # Notifiche mobile
│   └── desktop_notification_service.dart # Notifiche desktop
└── widgets/
    └── note_card.dart       # Widget card per le note
```

### Rilevamento Piattaforma
L'applicazione rileva automaticamente la piattaforma e carica l'interfaccia appropriata:
- **Mobile** (Android/iOS): HomeScreen con layout verticale
- **Desktop** (Windows/macOS/Linux): DesktopHomeScreen con layout a tre pannelli

### Database
- **Mobile**: SQLite tramite sqflite
- **Desktop**: SQLite tramite sqflite_common_ffi
- Schema unificato per tutte le piattaforme
- Migrazione automatica del database

## Utilizzo

### Aggiungere una Nota
1. Clicca il pulsante "+" (mobile) o "Aggiungi Nota" (desktop)
2. Inserisci titolo, descrizione e data di scadenza
3. Opzionalmente specifica un destinatario email
4. Salva la nota

### Configurare le Email
1. Accedi alle impostazioni
2. Configura i parametri SMTP del tuo provider email
3. Testa la configurazione inviando un'email di prova

### Gestire le Scadenze
- Le note in scadenza oggi vengono evidenziate
- Le email vengono inviate automaticamente alla data di scadenza
- Su desktop: controllo ogni ora
- Su mobile: controllo in background tramite WorkManager

## Supporto Piattaforme

| Piattaforma | Supporto | Note |
|-------------|----------|------|
| Android     | ✅ Completo | Notifiche background native |
| iOS         | ✅ Completo | Notifiche background native |
| Windows     | ✅ Completo | Controllo periodico ogni ora |
| macOS       | ✅ Completo | Controllo periodico ogni ora |
| Linux       | ✅ Completo | Controllo periodico ogni ora |
| Web         | ⚠️ Limitato | Nessun database persistente |

## Licenza

Questo progetto è rilasciato sotto licenza MIT.

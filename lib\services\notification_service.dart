import 'package:workmanager/workmanager.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'database_helper.dart';
import 'email_service.dart';

class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  static const String _taskName = 'checkDueNotes';
  static const String _uniqueName = 'checkDueNotesTask';

  Future<void> initialize() async {
    await Workmanager().initialize(
      callbackDispatcher,
      isInDebugMode: false,
    );
  }

  Future<void> schedulePeriodicCheck() async {
    await Workmanager().registerPeriodicTask(
      _uniqueName,
      _taskName,
      frequency: const Duration(hours: 1), // Controlla ogni ora
      constraints: Constraints(
        networkType: NetworkType.connected,
      ),
    );
  }

  Future<void> cancelPeriodicCheck() async {
    await Workmanager().cancelByUniqueName(_uniqueName);
  }

  Future<void> checkAndSendNotifications() async {
    try {
      final dbHelper = DatabaseHelper();
      final emailService = EmailService();
      
      // Ottieni le note in scadenza oggi
      final todayNotes = await dbHelper.getNotesForToday();
      
      if (todayNotes.isNotEmpty) {
        // Controlla se abbiamo già inviato le notifiche oggi
        final prefs = await SharedPreferences.getInstance();
        final lastNotificationDate = prefs.getString('last_notification_date');
        final today = DateTime.now().toIso8601String().substring(0, 10);
        
        if (lastNotificationDate != today) {
          // Invia le notifiche email
          final success = await emailService.sendMultipleDueNotifications(todayNotes);
          
          if (success) {
            // Salva la data dell'ultima notifica
            await prefs.setString('last_notification_date', today);
            print('Notifiche inviate per ${todayNotes.length} note');
          }
        }
      }
    } catch (e) {
      print('Errore nel controllo delle scadenze: $e');
    }
  }
}

// Callback per il background task
@pragma('vm:entry-point')
void callbackDispatcher() {
  Workmanager().executeTask((task, inputData) async {
    try {
      switch (task) {
        case NotificationService._taskName:
          final notificationService = NotificationService();
          await notificationService.checkAndSendNotifications();
          break;
      }
      return Future.value(true);
    } catch (e) {
      print('Errore nel task in background: $e');
      return Future.value(false);
    }
  });
}

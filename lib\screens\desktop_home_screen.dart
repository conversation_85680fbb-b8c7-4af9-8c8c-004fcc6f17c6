import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/note_model.dart';
import '../services/database_helper.dart';
import '../services/desktop_notification_service.dart';
import 'add_note_screen.dart';
import 'settings_screen.dart';

class DesktopHomeScreen extends StatefulWidget {
  const DesktopHomeScreen({super.key});

  @override
  State<DesktopHomeScreen> createState() => _DesktopHomeScreenState();
}

class _DesktopHomeScreenState extends State<DesktopHomeScreen> {
  final DatabaseHelper _dbHelper = DatabaseHelper();
  final DesktopNotificationService _notificationService =
      DesktopNotificationService();
  List<NoteModel> _notes = [];
  String _selectedFilter = 'Tutte';
  NoteModel? _selectedNote;

  @override
  void initState() {
    super.initState();
    _loadNotes();
    _initializeNotifications();
  }

  Future<void> _initializeNotifications() async {
    await _notificationService.initialize();
    await _notificationService.schedulePeriodicCheck();
  }

  Future<void> _loadNotes() async {
    final notes = await _dbHelper.getAllNotes();
    setState(() {
      _notes = notes;
    });
  }

  List<NoteModel> _getFilteredNotes() {
    switch (_selectedFilter) {
      case 'Oggi':
        return _notes
            .where((note) => note.isDueToday() && !note.isCompleted)
            .toList();
      case 'Scadute':
        return _notes.where((note) => note.isOverdue()).toList();
      case 'Completate':
        return _notes.where((note) => note.isCompleted).toList();
      default:
        return _notes;
    }
  }

  @override
  Widget build(BuildContext context) {
    final filteredNotes = _getFilteredNotes();

    return Scaffold(
      appBar: AppBar(
        title: const Text('Scadenziario Desktop'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () async {
              await _notificationService.checkAndSendNotifications();
              _loadNotes();
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Controllo scadenze completato'),
                  ),
                );
              }
            },
            tooltip: 'Controlla scadenze',
          ),
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _showAddNoteDialog(),
            tooltip: 'Nuova nota',
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () => _showSettingsDialog(),
            tooltip: 'Impostazioni',
          ),
        ],
      ),
      body: Row(
        children: [
          // Sidebar con filtri
          Container(
            width: 250,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              border: Border(
                right: BorderSide(
                  color: Theme.of(context).dividerColor,
                  width: 1,
                ),
              ),
            ),
            child: Column(
              children: [
                const Padding(
                  padding: EdgeInsets.all(16.0),
                  child: Text(
                    'Filtri',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                ),
                _buildFilterTile('Tutte', Icons.list, _notes.length),
                _buildFilterTile(
                  'Oggi',
                  Icons.today,
                  _notes.where((n) => n.isDueToday() && !n.isCompleted).length,
                ),
                _buildFilterTile(
                  'Scadute',
                  Icons.warning,
                  _notes.where((n) => n.isOverdue()).length,
                ),
                _buildFilterTile(
                  'Completate',
                  Icons.check_circle,
                  _notes.where((n) => n.isCompleted).length,
                ),
                const Divider(),
                const Spacer(),
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      Text(
                        'Totale note: ${_notes.length}',
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Ultimo aggiornamento:\n${DateFormat('dd/MM/yyyy HH:mm').format(DateTime.now())}',
                        style: Theme.of(context).textTheme.bodySmall,
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          // Lista note
          Expanded(
            flex: 2,
            child: Column(
              children: [
                Container(
                  padding: const EdgeInsets.all(16.0),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surface,
                    border: Border(
                      bottom: BorderSide(
                        color: Theme.of(context).dividerColor,
                        width: 1,
                      ),
                    ),
                  ),
                  child: Row(
                    children: [
                      Text(
                        '$_selectedFilter (${filteredNotes.length})',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const Spacer(),
                      if (filteredNotes.isNotEmpty)
                        Text(
                          'Seleziona una nota per visualizzare i dettagli',
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                    ],
                  ),
                ),
                Expanded(
                  child: filteredNotes.isEmpty
                      ? _buildEmptyState()
                      : ListView.builder(
                          itemCount: filteredNotes.length,
                          itemBuilder: (context, index) {
                            final note = filteredNotes[index];
                            return _buildNoteListTile(note);
                          },
                        ),
                ),
              ],
            ),
          ),
          // Pannello dettagli
          if (_selectedNote != null)
            Container(
              width: 350,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                border: Border(
                  left: BorderSide(
                    color: Theme.of(context).dividerColor,
                    width: 1,
                  ),
                ),
              ),
              child: _buildNoteDetails(_selectedNote!),
            ),
        ],
      ),
    );
  }

  Widget _buildFilterTile(String title, IconData icon, int count) {
    final isSelected = _selectedFilter == title;

    return ListTile(
      leading: Icon(
        icon,
        color: isSelected ? Theme.of(context).colorScheme.primary : null,
      ),
      title: Text(title),
      trailing: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: isSelected
              ? Theme.of(context).colorScheme.primary
              : Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Text(
          count.toString(),
          style: TextStyle(
            color: isSelected
                ? Theme.of(context).colorScheme.onPrimary
                : Theme.of(context).colorScheme.onSurface,
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      selected: isSelected,
      onTap: () {
        setState(() {
          _selectedFilter = title;
          _selectedNote = null;
        });
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.note_add, size: 64, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            'Nessuna nota trovata',
            style: TextStyle(fontSize: 18, color: Colors.grey[600]),
          ),
          const SizedBox(height: 8),
          Text(
            'Clicca il pulsante + per aggiungere una nuova nota',
            style: TextStyle(color: Colors.grey[500]),
          ),
        ],
      ),
    );
  }

  Widget _buildNoteListTile(NoteModel note) {
    Color cardColor = Colors.white;
    Color borderColor = Colors.grey[300]!;
    IconData statusIcon = Icons.schedule;
    Color statusColor = Colors.blue;

    if (note.isCompleted) {
      cardColor = Colors.green[50]!;
      borderColor = Colors.green[300]!;
      statusIcon = Icons.check_circle;
      statusColor = Colors.green;
    } else if (note.isOverdue()) {
      cardColor = Colors.red[50]!;
      borderColor = Colors.red[300]!;
      statusIcon = Icons.warning;
      statusColor = Colors.red;
    } else if (note.isDueToday()) {
      cardColor = Colors.orange[50]!;
      borderColor = Colors.orange[300]!;
      statusIcon = Icons.today;
      statusColor = Colors.orange;
    }

    final isSelected = _selectedNote?.id == note.id;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: isSelected
            ? Theme.of(context).colorScheme.primaryContainer
            : cardColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isSelected
              ? Theme.of(context).colorScheme.primary
              : borderColor,
          width: isSelected ? 2 : 1,
        ),
      ),
      child: ListTile(
        leading: Icon(statusIcon, color: statusColor),
        title: Text(
          note.title,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            decoration: note.isCompleted ? TextDecoration.lineThrough : null,
          ),
        ),
        subtitle: Text(
          'Scadenza: ${DateFormat('dd/MM/yyyy').format(note.dueDate)}',
          style: TextStyle(color: statusColor, fontWeight: FontWeight.w500),
        ),
        onTap: () {
          setState(() {
            _selectedNote = note;
          });
        },
      ),
    );
  }

  Widget _buildNoteDetails(NoteModel note) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.all(16.0),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primaryContainer,
            border: Border(
              bottom: BorderSide(
                color: Theme.of(context).dividerColor,
                width: 1,
              ),
            ),
          ),
          child: Row(
            children: [
              const Text(
                'Dettagli Nota',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const Spacer(),
              IconButton(
                icon: const Icon(Icons.close),
                onPressed: () {
                  setState(() {
                    _selectedNote = null;
                  });
                },
                tooltip: 'Chiudi dettagli',
              ),
            ],
          ),
        ),
        Expanded(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildDetailRow('Titolo', note.title),
                const SizedBox(height: 16),
                _buildDetailRow('Descrizione', note.description),
                const SizedBox(height: 16),
                _buildDetailRow(
                  'Data di scadenza',
                  DateFormat('dd/MM/yyyy').format(note.dueDate),
                ),
                const SizedBox(height: 16),
                _buildDetailRow(
                  'Stato',
                  note.isCompleted ? 'Completata' : 'In corso',
                ),
                const SizedBox(height: 16),
                if (note.emailRecipient != null &&
                    note.emailRecipient!.isNotEmpty)
                  _buildDetailRow('Email destinatario', note.emailRecipient!),
                const SizedBox(height: 16),
                _buildDetailRow(
                  'Creata il',
                  DateFormat('dd/MM/yyyy HH:mm').format(note.createdAt),
                ),
                const Spacer(),
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: note.isCompleted
                            ? null
                            : () => _markAsCompleted(note),
                        icon: const Icon(Icons.check),
                        label: const Text('Completa'),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () => _editNote(note),
                        icon: const Icon(Icons.edit),
                        label: const Text('Modifica'),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: () => _deleteNote(note),
                    icon: const Icon(Icons.delete),
                    label: const Text('Elimina'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
        ),
        const SizedBox(height: 4),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Theme.of(context).dividerColor),
          ),
          child: Text(value, style: const TextStyle(fontSize: 14)),
        ),
      ],
    );
  }

  Future<void> _markAsCompleted(NoteModel note) async {
    await _dbHelper.markAsCompleted(note.id!);
    _loadNotes();
    setState(() {
      _selectedNote = null;
    });
    if (mounted) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('Nota completata!')));
    }
  }

  Future<void> _editNote(NoteModel note) async {
    await _showAddNoteDialog(note: note);
  }

  Future<void> _deleteNote(NoteModel note) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Conferma eliminazione'),
        content: Text('Sei sicuro di voler eliminare la nota "${note.title}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Annulla'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Elimina'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await _dbHelper.deleteNote(note.id!);
      _loadNotes();
      setState(() {
        _selectedNote = null;
      });
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('Nota eliminata!')));
      }
    }
  }

  Future<void> _showAddNoteDialog({NoteModel? note}) async {
    await showDialog(
      context: context,
      builder: (context) => Dialog(
        child: SizedBox(
          width: 500,
          height: 600,
          child: AddNoteScreen(noteToEdit: note),
        ),
      ),
    );
    _loadNotes();
    setState(() {
      _selectedNote = null;
    });
  }

  Future<void> _showSettingsDialog() async {
    await showDialog(
      context: context,
      builder: (context) => Dialog(
        child: SizedBox(width: 600, height: 700, child: const SettingsScreen()),
      ),
    );
  }
}

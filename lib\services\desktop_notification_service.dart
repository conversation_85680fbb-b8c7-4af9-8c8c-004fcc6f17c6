import 'dart:async';
import 'package:flutter/foundation.dart';
import '../models/note_model.dart';
import 'database_helper.dart';
import 'email_service.dart';

class DesktopNotificationService {
  static final DesktopNotificationService _instance =
      DesktopNotificationService._internal();
  factory DesktopNotificationService() => _instance;
  DesktopNotificationService._internal();

  final DatabaseHelper _dbHelper = DatabaseHelper();
  final EmailService _emailService = EmailService();
  Timer? _periodicTimer;
  bool _isInitialized = false;

  Future<void> initialize() async {
    if (_isInitialized) return;

    if (kDebugMode) {
      print('Inizializzazione DesktopNotificationService...');
    }

    _isInitialized = true;
  }

  Future<void> schedulePeriodicCheck() async {
    // Cancella il timer esistente se presente
    _periodicTimer?.cancel();

    // Crea un nuovo timer che controlla ogni ora
    _periodicTimer = Timer.periodic(const Duration(hours: 1), (timer) {
      checkAndSendNotifications();
    });

    // Esegui anche un controllo immediato
    await checkAndSendNotifications();

    if (kDebugMode) {
      print('Controllo periodico delle notifiche attivato (ogni ora)');
    }
  }

  Future<void> cancelPeriodicCheck() async {
    _periodicTimer?.cancel();
    _periodicTimer = null;

    if (kDebugMode) {
      print('Controllo periodico delle notifiche disattivato');
    }
  }

  Future<void> checkAndSendNotifications() async {
    try {
      if (kDebugMode) {
        print('Controllo notifiche in corso...');
      }

      // Ottieni le note in scadenza oggi
      final todayNotes = await _dbHelper.getNotesForToday();
      final dueTodayNotes = todayNotes
          .where((note) => !note.isCompleted)
          .toList();

      if (dueTodayNotes.isEmpty) {
        if (kDebugMode) {
          print('Nessuna nota in scadenza oggi');
        }
        return;
      }

      if (kDebugMode) {
        print('Trovate ${dueTodayNotes.length} note in scadenza oggi');
      }

      // Raggruppa le note per destinatario email
      final Map<String, List<NoteModel>> notesByRecipient = {};

      for (final note in dueTodayNotes) {
        final recipient = note.emailRecipient?.isNotEmpty == true
            ? note.emailRecipient!
            : 'default';

        if (!notesByRecipient.containsKey(recipient)) {
          notesByRecipient[recipient] = [];
        }
        notesByRecipient[recipient]!.add(note);
      }

      // Invia le notifiche per ogni destinatario
      for (final entry in notesByRecipient.entries) {
        final recipient = entry.key;
        final notes = entry.value;

        if (recipient == 'default') {
          // Invia email singole per le note senza destinatario specifico
          for (final note in notes) {
            final success = await _emailService.sendDueNotification(note);
            if (kDebugMode) {
              print(
                'Email per "${note.title}": ${success ? "inviata" : "fallita"}',
              );
            }
          }
        } else {
          // Invia email raggruppata per destinatario specifico
          final success = await _emailService.sendMultipleDueNotifications(
            notes,
          );
          if (kDebugMode) {
            print(
              'Email raggruppata per $recipient: ${success ? "inviata" : "fallita"}',
            );
          }
        }
      }

      if (kDebugMode) {
        print('Controllo notifiche completato');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Errore durante il controllo delle notifiche: $e');
      }
    }
  }

  void dispose() {
    _periodicTimer?.cancel();
    _periodicTimer = null;
  }
}

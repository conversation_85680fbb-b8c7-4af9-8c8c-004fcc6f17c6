import 'package:mailer/mailer.dart';
import 'package:mailer/smtp_server.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/note_model.dart';

class EmailService {
  static final EmailService _instance = EmailService._internal();
  factory EmailService() => _instance;
  EmailService._internal();

  // Le configurazioni email vengono salvate nelle SharedPreferences

  Future<void> saveEmailSettings({
    required String senderEmail,
    required String senderPassword,
    required String smtpHost,
    required int smtpPort,
  }) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('sender_email', senderEmail);
    await prefs.setString('sender_password', senderPassword);
    await prefs.setString('smtp_host', smtpHost);
    await prefs.setInt('smtp_port', smtpPort);
  }

  Future<Map<String, dynamic>?> getEmailSettings() async {
    final prefs = await SharedPreferences.getInstance();
    final senderEmail = prefs.getString('sender_email');
    final senderPassword = prefs.getString('sender_password');
    final smtpHost = prefs.getString('smtp_host');
    final smtpPort = prefs.getInt('smtp_port');

    if (senderEmail == null ||
        senderPassword == null ||
        smtpHost == null ||
        smtpPort == null) {
      return null;
    }

    return {
      'senderEmail': senderEmail,
      'senderPassword': senderPassword,
      'smtpHost': smtpHost,
      'smtpPort': smtpPort,
    };
  }

  Future<bool> sendDueNotification(NoteModel note) async {
    try {
      final settings = await getEmailSettings();
      if (settings == null) {
        print('Configurazione email non trovata');
        return false;
      }

      final recipientEmail = note.emailRecipient ?? settings['senderEmail'];

      final smtpServer = _createSmtpServer(settings);

      final message = Message()
        ..from = Address(settings['senderEmail'], 'Scadenziario App')
        ..recipients.add(recipientEmail)
        ..subject = '⏰ Promemoria Scadenza: ${note.title}'
        ..html = _buildEmailHtml(note);

      final sendReport = await send(message, smtpServer);
      print('Email inviata: ${sendReport.toString()}');
      return true;
    } catch (e) {
      print('Errore nell\'invio dell\'email: $e');
      return false;
    }
  }

  Future<bool> sendTestEmail() async {
    try {
      final settings = await getEmailSettings();
      if (settings == null) {
        print('Configurazione email non trovata');
        return false;
      }

      final smtpServer = _createSmtpServer(settings);

      final message = Message()
        ..from = Address(settings['senderEmail'], 'Scadenziario App')
        ..recipients.add(settings['senderEmail'])
        ..subject = '✅ Test Email - Configurazione Funzionante'
        ..html = _buildTestEmailHtml();

      final sendReport = await send(message, smtpServer);
      print('Email di test inviata: ${sendReport.toString()}');
      return true;
    } catch (e) {
      print('Errore nell\'invio dell\'email di test: $e');
      rethrow; // Rilancia l'errore per mostrarlo all'utente
    }
  }

  SmtpServer _createSmtpServer(Map<String, dynamic> settings) {
    final host = settings['smtpHost'] as String;
    final port = settings['smtpPort'] as int;

    // Configurazione ottimizzata per diversi provider
    if (host.contains('gmail.com')) {
      return gmail(settings['senderEmail'], settings['senderPassword']);
    } else if (host.contains('outlook.com') || host.contains('hotmail.com') || host.contains('live.com')) {
      return hotmail(settings['senderEmail'], settings['senderPassword']);
    } else if (host.contains('yahoo.com')) {
      return yahoo(settings['senderEmail'], settings['senderPassword']);
    } else {
      // Configurazione generica con STARTTLS
      return SmtpServer(
        host,
        port: port,
        username: settings['senderEmail'],
        password: settings['senderPassword'],
        allowInsecure: false,
        ssl: port == 465, // SSL per porta 465, altrimenti STARTTLS
        ignoreBadCertificate: false,
      );
    }
  }

  Future<bool> sendMultipleDueNotifications(List<NoteModel> notes) async {
    if (notes.isEmpty) return true;

    try {
      final settings = await getEmailSettings();
      if (settings == null) {
        print('Configurazione email non trovata');
        return false;
      }

      // Raggruppa le note per destinatario
      final Map<String, List<NoteModel>> notesByRecipient = {};
      for (final note in notes) {
        final recipient = note.emailRecipient ?? settings['senderEmail'];
        notesByRecipient.putIfAbsent(recipient, () => []).add(note);
      }

      final smtpServer = _createSmtpServer(settings);

      // Invia un'email per ogni destinatario
      for (final entry in notesByRecipient.entries) {
        final recipient = entry.key;
        final recipientNotes = entry.value;

        final message = Message()
          ..from = Address(settings['senderEmail'], 'Scadenziario App')
          ..recipients.add(recipient)
          ..subject =
              '⏰ Promemoria Scadenze - ${recipientNotes.length} note in scadenza'
          ..html = _buildMultipleNotesEmailHtml(recipientNotes);

        await send(message, smtpServer);
      }

      print('Email inviate per ${notes.length} note');
      return true;
    } catch (e) {
      print('Errore nell\'invio delle email: $e');
      return false;
    }
  }

  String _buildEmailHtml(NoteModel note) {
    return '''
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
            .container { max-width: 600px; margin: 0 auto; background-color: white; border-radius: 10px; padding: 30px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            .header { text-align: center; color: #d32f2f; margin-bottom: 30px; }
            .note-card { background-color: #fff3e0; border-left: 4px solid #ff9800; padding: 20px; margin: 20px 0; border-radius: 5px; }
            .note-title { font-size: 20px; font-weight: bold; color: #333; margin-bottom: 10px; }
            .note-description { color: #666; line-height: 1.6; margin-bottom: 15px; }
            .due-date { color: #d32f2f; font-weight: bold; font-size: 16px; }
            .footer { text-align: center; margin-top: 30px; color: #999; font-size: 12px; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🔔 Promemoria Scadenza</h1>
            </div>
            <div class="note-card">
                <div class="note-title">${note.title}</div>
                <div class="note-description">${note.description}</div>
                <div class="due-date">📅 Scadenza: ${_formatDate(note.dueDate)}</div>
            </div>
            <div class="footer">
                <p>Questo è un promemoria automatico dalla tua app Scadenziario</p>
            </div>
        </div>
    </body>
    </html>
    ''';
  }

  String _buildMultipleNotesEmailHtml(List<NoteModel> notes) {
    final notesHtml = notes
        .map(
          (note) =>
              '''
        <div class="note-card">
            <div class="note-title">${note.title}</div>
            <div class="note-description">${note.description}</div>
            <div class="due-date">📅 Scadenza: ${_formatDate(note.dueDate)}</div>
        </div>
    ''',
        )
        .join('');

    return '''
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
            .container { max-width: 600px; margin: 0 auto; background-color: white; border-radius: 10px; padding: 30px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            .header { text-align: center; color: #d32f2f; margin-bottom: 30px; }
            .note-card { background-color: #fff3e0; border-left: 4px solid #ff9800; padding: 20px; margin: 20px 0; border-radius: 5px; }
            .note-title { font-size: 18px; font-weight: bold; color: #333; margin-bottom: 10px; }
            .note-description { color: #666; line-height: 1.6; margin-bottom: 15px; }
            .due-date { color: #d32f2f; font-weight: bold; font-size: 14px; }
            .footer { text-align: center; margin-top: 30px; color: #999; font-size: 12px; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🔔 Promemoria Scadenze</h1>
                <p>Hai ${notes.length} note in scadenza oggi</p>
            </div>
            $notesHtml
            <div class="footer">
                <p>Questo è un promemoria automatico dalla tua app Scadenziario</p>
            </div>
        </div>
    </body>
    </html>
    ''';
  }

  String _buildTestEmailHtml() {
    return '''
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
            .container { max-width: 600px; margin: 0 auto; background-color: white; border-radius: 10px; padding: 30px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            .header { text-align: center; color: #4caf50; margin-bottom: 30px; }
            .success-card { background-color: #e8f5e8; border-left: 4px solid #4caf50; padding: 20px; margin: 20px 0; border-radius: 5px; }
            .success-title { font-size: 20px; font-weight: bold; color: #333; margin-bottom: 10px; }
            .success-description { color: #666; line-height: 1.6; margin-bottom: 15px; }
            .footer { text-align: center; margin-top: 30px; color: #999; font-size: 12px; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>✅ Test Email Riuscito!</h1>
            </div>
            <div class="success-card">
                <div class="success-title">Configurazione Email Funzionante</div>
                <div class="success-description">
                    Congratulazioni! La configurazione email è stata impostata correttamente e l'invio di email funziona perfettamente.
                    <br><br>
                    Ora puoi ricevere notifiche automatiche per le tue scadenze.
                </div>
            </div>
            <div class="footer">
                <p>Questo è un messaggio di test dalla tua app Scadenziario</p>
                <p>Data: ${DateTime.now().day.toString().padLeft(2, '0')}/${DateTime.now().month.toString().padLeft(2, '0')}/${DateTime.now().year} - ${DateTime.now().hour.toString().padLeft(2, '0')}:${DateTime.now().minute.toString().padLeft(2, '0')}</p>
            </div>
        </div>
    </body>
    </html>
    ''';
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }
}

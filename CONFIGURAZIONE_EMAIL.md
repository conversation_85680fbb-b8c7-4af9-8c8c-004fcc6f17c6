# 📧 Guida alla Configurazione Email

Questa guida ti aiuterà a configurare l'invio automatico di email per le notifiche di scadenza.

## 🔧 Configurazione per Gmail

### Passo 1: Attiva l'Autenticazione a 2 Fattori

1. Vai su [myaccount.google.com](https://myaccount.google.com)
2. Clicca su **"Sicurezza"** nel menu laterale
3. Nella sezione **"Accesso a Google"**, clicca su **"Verifica in due passaggi"**
4. Segui le istruzioni per attivare l'autenticazione a 2 fattori

### Passo 2: Genera una Password per App

1. Torna alla sezione **"Sicurezza"**
2. <PERSON><PERSON><PERSON> su **"Password per le app"** (apparirà solo dopo aver attivato la 2FA)
3. Seleziona **"App"** → **"Altra (nome personalizzato)"**
4. Inserisci un nome come "Scadenziario App"
5. Clicca su **"Genera"**
6. **Copia la password generata** (16 caratteri senza spazi)

### Passo 3: Configura l'App

1. Apri l'app Scadenziario
2. Vai su **Impostazioni** (icona ingranaggio)
3. Nella sezione **"Configurazione Email"** inserisci:
   - **Email mittente**: la tua email Gmail (es: `<EMAIL>`)
   - **Password app**: la password di 16 caratteri generata al passo 2
   - **Server SMTP**: `smtp.gmail.com`
   - **Porta SMTP**: `587`
4. Tocca **"Test Email"** per verificare la configurazione
5. Tocca **"Salva"** per salvare le impostazioni

## 🔧 Configurazione per Altri Provider

### Outlook/Hotmail

- **Server SMTP**: `smtp-mail.outlook.com`
- **Porta SMTP**: `587`
- **Email mittente**: la tua email Outlook
- **Password**: la password del tuo account Outlook

### Yahoo Mail

- **Server SMTP**: `smtp.mail.yahoo.com`
- **Porta SMTP**: `587`
- **Email mittente**: la tua email Yahoo
- **Password**: password specifica per app (da generare nelle impostazioni Yahoo)

### Provider Personalizzati

Per altri provider email, consulta la documentazione del tuo provider per:
- Indirizzo del server SMTP
- Porta SMTP (solitamente 587 o 465)
- Tipo di autenticazione richiesta

## ⚠️ Risoluzione Problemi

### "Errore nell'invio dell'email"

1. **Verifica le credenziali**: Assicurati che email e password siano corretti
2. **Controlla la connessione**: Verifica di essere connesso a internet
3. **Password per app**: Per Gmail, usa la password per app, non quella normale
4. **Firewall**: Controlla che l'app possa accedere a internet

### Errori Comuni e Soluzioni

#### "Authentication failed" / "Credenziali non valide"
- **Gmail**: Assicurati di aver generato una Password per App e di usare quella invece della password normale
- **Outlook**: Verifica che la password sia corretta e che l'account non abbia restrizioni
- **Yahoo**: Genera una Password specifica per app dalle impostazioni di sicurezza

#### "Connection failed" / "Impossibile connettersi"
- Verifica che il server SMTP e la porta siano corretti
- Controlla la connessione internet
- Alcuni firewall aziendali potrebbero bloccare le connessioni SMTP

#### "Certificate error" / "Errore certificato SSL"
- Questo errore è raro con i provider principali
- Verifica che stai usando le impostazioni corrette per il tuo provider

### Test della Configurazione

1. Usa i pulsanti di configurazione rapida (Gmail, Outlook, Yahoo) per impostare automaticamente server e porta
2. Inserisci la tua email e password (o password per app)
3. Clicca su "Test Email" per verificare che tutto funzioni
4. Se il test ha successo, clicca su "Salva" per salvare le impostazioni

### Configurazioni Rapide Disponibili

L'app ora include configurazioni predefinite per:
- **Gmail**: smtp.gmail.com:587 (richiede Password per App)
- **Outlook**: smtp-mail.outlook.com:587 (usa password normale)
- **Yahoo**: smtp.mail.yahoo.com:587 (richiede Password specifica per app)

### "Configurazione email non trovata"

1. Vai nelle **Impostazioni**
2. Inserisci nuovamente tutti i dati email
3. Tocca **"Salva"** prima di testare

### Le notifiche non arrivano

1. Controlla che le **notifiche automatiche** siano attivate nelle impostazioni
2. Verifica che l'app abbia i permessi per funzionare in background
3. Su Android, disabilita l'ottimizzazione della batteria per l'app

## 🔒 Sicurezza

- **Non condividere mai** la password per app con altri
- **Revoca le password** per app non utilizzate dalle impostazioni Google
- **Usa sempre** l'autenticazione a 2 fattori per maggiore sicurezza

## 📱 Test della Configurazione

Dopo aver configurato l'email:

1. Crea una nota di test con scadenza oggi
2. Tocca l'icona **"Aggiorna"** nella schermata principale
3. Controlla la tua casella email per la notifica
4. Se non ricevi l'email, verifica la configurazione

## 💡 Suggerimenti

- **Email destinatario**: Puoi specificare un'email diversa per ogni nota
- **Controllo automatico**: L'app controlla le scadenze ogni ora automaticamente
- **Backup**: Salva le tue impostazioni email in un posto sicuro
- **Test regolari**: Testa periodicamente l'invio email per assicurarti che funzioni

## 📞 Supporto

Se hai problemi con la configurazione email:

1. Verifica di aver seguito tutti i passaggi correttamente
2. Controlla la documentazione del tuo provider email
3. Prova con un account Gmail per testare la funzionalità
4. Apri un issue su GitHub per supporto tecnico

---

**Nota**: Questa app è progettata per uso personale. Non utilizzare credenziali email aziendali senza autorizzazione.
